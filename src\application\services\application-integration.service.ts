/**
 * Application Integration Service
 *
 * Streamlined service for creating application records from successful payments.
 * Updated to use the simplified ApplicationService for consistency.
 *
 * Key Features:
 * - Automatic application creation after successful payments
 * - Delegates to ApplicationService for consistency
 * - Workflow template validation
 * - Comprehensive error handling and logging
 */

import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../utils/prisma.service';
import { LoggerService } from '../../utils/logger.service';
import {
  ApplicationService,
  CreateApplicationData,
} from '../application.service';

// REMOVED: Duplicate interface - using CreateApplicationData from ApplicationService
// export interface ApplicationCreationData extends CreateApplicationData {}

@Injectable()
export class ApplicationIntegrationService {
  private readonly logger = new Logger(ApplicationIntegrationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly loggerService: LoggerService,
    private readonly applicationService: ApplicationService,
  ) {}

  /**
   * Create application record from payment data
   *
   * Creates a new application record when a service payment is successful.
   * This method delegates to ApplicationService for consistency.
   *
   * @param data - Application creation data from payment
   * @returns Promise<any> - Created application record
   */
  async createApplicationFromPayment(
    data: CreateApplicationData,
  ): Promise<any> {
    try {
      this.logger.log(`Creating application from payment: ${data.paymentId}`);

      // Delegate to ApplicationService for consistency
      const application =
        await this.applicationService.createApplicationFromPayment(data);

      this.logger.log(
        `Application created via ApplicationService: ${application.id} (${application.application_number})`,
      );

      return application;
    } catch (error) {
      this.logger.error(
        `Failed to create application from payment: ${data.paymentId}`,
        error,
      );
      throw error;
    }
  }

  // REMOVED: Duplicate methods - now handled by ApplicationService
  // - generateApplicationNumber()
  // - getApplicationPrefix()

  /**
   * Validate workflow template exists for service
   *
   * @param serviceType - Service type
   * @param serviceId - Service ID
   * @returns Promise<string | null> - Workflow template ID or null if not found
   */
  async validateWorkflowTemplate(
    serviceType: string,
    serviceId: string,
  ): Promise<string | null> {
    try {
      // First, try to find a specific workflow template for this service
      let workflowTemplate = await this.prisma.workflow_template.findFirst({
        where: {
          serviceType: serviceType,
          serviceId: serviceId,
          isActive: true,
          isDefault: true,
        },
      });

      // If no specific template found, look for a general template
      // if (!workflowTemplate) {
      //   workflowTemplate = await this.prisma.workflow_template.findFirst({
      //     where: {
      //       serviceType: serviceType,
      //       serviceId: null, // General template
      //       isActive: true,
      //     },
      //   });
      // }

      if (workflowTemplate) {
        this.logger.log(
          `Found workflow template: ${workflowTemplate.id} for service: ${serviceType}/${serviceId}`,
        );
        return workflowTemplate.id;
      }

      this.logger.warn(
        `No workflow template found for service: ${serviceType}/${serviceId}`,
      );
      return null;
    } catch (error) {
      this.logger.error(
        `Failed to validate workflow template for service: ${serviceType}/${serviceId}`,
        error,
      );
      return null;
    }
  }
}
